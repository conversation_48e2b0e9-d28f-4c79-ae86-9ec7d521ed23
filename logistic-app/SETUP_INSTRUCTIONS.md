# Logistics App Setup Instructions

## Quick Setup for Your Presentation

### 1. Get Google Maps API Key

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Enable the following APIs:
   - Maps JavaScript API
   - Geolocation API
4. Go to "Credentials" and create an API key
5. Copy your API key

### 2. Configure the API Key

1. Open the `.env` file in the `logistic-app` folder
2. Replace `your_google_maps_api_key_here` with your actual API key:
   ```
   VITE_GOOGLE_MAPS_API_KEY=AIzaSyBvOkBwgdtgdN0wNiuAOsn_86C6eLlag7c
   ```
   (Use your actual key, not this example)

### 3. Run the Application

The app is already running! Just:
1. Make sure you've updated the `.env` file with your API key
2. Refresh the browser page at http://localhost:5174/
3. Allow location access when prompted by your browser

### 4. Features

- **Real-time Location**: Shows your current GPS location on the map
- **Clean Interface**: Simple, professional design perfect for presentations
- **Responsive**: Works on desktop and mobile devices
- **Location Status**: Shows your coordinates and GPS status

### 5. For Your Presentation

- The app will automatically detect and show your current location
- The map centers on your location with a blue marker
- Location coordinates are displayed in the sidebar
- Perfect for demonstrating real-time logistics tracking

### 6. Troubleshooting

- **Map not loading**: Check your API key in the `.env` file
- **Location not found**: Make sure to allow location access in your browser
- **App not working**: Restart the dev server with `npm run dev`

### 7. Browser Location Permission

When you first load the app, your browser will ask for location permission. Make sure to:
1. Click "Allow" when prompted
2. If you accidentally clicked "Block", go to browser settings and enable location for localhost

That's it! Your logistics app with Google Maps integration is ready for your presentation.













✅ STEP 1: Get a Google Maps API Key

    Go to the Google Cloud Console.

    Create a new project (or use an existing one).

    Navigate to:
    APIs & Services > Credentials.

    Click “+ Create Credentials” > select API Key.

    Copy the key that appears.

✅ STEP 2: Enable Required APIs

To use Maps and Geolocation:

    Go to APIs & Services > Library

    Enable the following:

        Maps JavaScript API

        Geocoding API

        Places API (optional but useful)

        Geolocation API

✅ STEP 3: Add API Key to Your Code
📍 For Web: Google Maps JavaScript (in HTML)

<script src="https://maps.googleapis.com/maps/api/js?key=YOUR_API_KEY&callback=initMap" async defer></script>

Replace YOUR_API_KEY with your actual key.
📍 Example initMap() Function

<script>
  function initMap() {
    const map = new google.maps.Map(document.getElementById("map"), {
      center: { lat: -1.286389, lng: 36.817223 }, // Nairobi, Kenya
      zoom: 12,
    });

    // Geolocation
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition((position) => {
        const userPos = {
          lat: position.coords.latitude,
          lng: position.coords.longitude
        };
        new google.maps.Marker({
          position: userPos,
          map: map,
          title: "You are here"
        });
        map.setCenter(userPos);
      }, () => {
        alert("Geolocation failed.");
      });
    } else {
      alert("Geolocation is not supported by this browser.");
    }
  }
</script>

✅ STEP 4: Restrict Your API Key (Highly Recommended)

Go to:
APIs & Services > Credentials > API Key > Edit:

    HTTP Referrers: Restrict to your domain or localhost

    API Restrictions: Limit key to only the enabled APIs

⚠️ TIP: Handling API Keys Safely

    NEVER expose keys in public repos or client-side code for production.

    Use a proxy backend to call Google APIs securely if needed.

    Rotate keys periodically for security.

Would you like me to generate a working HTML + JS example for your use case?