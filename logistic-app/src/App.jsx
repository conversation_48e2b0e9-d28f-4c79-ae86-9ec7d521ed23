import { useState } from 'react';
import MapContainer from './components/MapContainer';
import LocationStatus from './components/LocationStatus';

function App() {
  const [userLocation, setUserLocation] = useState(null);
  const [error, setError] = useState(null);

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <h1 className="text-2xl font-bold text-center mb-4">Logistics Tracker</h1>
      <LocationStatus userLocation={userLocation} error={error} />
      <MapContainer 
        setUserLocation={setUserLocation} 
        setError={setError} 
        userLocation={userLocation}
      />
    </div>
  );
}

export default App;