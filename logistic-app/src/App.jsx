import { useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTruck, faMapMarkerAlt, faRoute } from '@fortawesome/free-solid-svg-icons';
import MapContainer from './components/MapContainer';
import LocationStatus from './components/LocationStatus';
import DeliveryPanel from './components/DeliveryPanel';
import RouteOptimization from './components/RouteOptimization';

function App() {
  const [userLocation, setUserLocation] = useState(null);
  const [error, setError] = useState(null);

  return (
    <div className="min-h-screen bg-gradient-to-br from-logistics-50 to-logistics-100">
      {/* Header */}
      <header className="bg-white shadow-logistics border-b border-logistics-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-3">
              <div className="bg-logistics-500 p-2 rounded-lg">
                <FontAwesomeIcon icon={faTruck} className="text-white text-xl" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">LogiTrack</h1>
                <p className="text-sm text-gray-600">Real-time Logistics Management</p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2 text-logistics-600">
                <FontAwesomeIcon icon={faMapMarkerAlt} />
                <span className="text-sm font-medium">Live Tracking</span>
              </div>
              <div className="flex items-center space-x-2 text-logistics-600">
                <FontAwesomeIcon icon={faRoute} />
                <span className="text-sm font-medium">Route Optimization</span>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="grid grid-cols-1 xl:grid-cols-12 gap-6">
          {/* Left Sidebar */}
          <div className="xl:col-span-3 space-y-6">
            <LocationStatus userLocation={userLocation} error={error} />

            {/* Quick Stats */}
            <div className="bg-white rounded-xl shadow-logistics p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Stats</h3>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Active Deliveries</span>
                  <span className="font-semibold text-logistics-600">3</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Completed Today</span>
                  <span className="font-semibold text-green-600">1</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Pending</span>
                  <span className="font-semibold text-orange-600">2</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Total Distance</span>
                  <span className="font-semibold text-purple-600">12.4 km</span>
                </div>
              </div>
            </div>

            <RouteOptimization userLocation={userLocation} />
          </div>

          {/* Map Area */}
          <div className="xl:col-span-6">
            <div className="bg-white rounded-xl shadow-logistics overflow-hidden">
              <div className="p-4 border-b border-gray-200">
                <h2 className="text-lg font-semibold text-gray-900">Live Map View</h2>
                <p className="text-sm text-gray-600">Track your current location and delivery routes</p>
              </div>
              <MapContainer
                setUserLocation={setUserLocation}
                setError={setError}
                userLocation={userLocation}
              />
            </div>
          </div>

          {/* Right Sidebar */}
          <div className="xl:col-span-3">
            <DeliveryPanel userLocation={userLocation} />
          </div>
        </div>
      </main>
    </div>
  );
}

export default App;