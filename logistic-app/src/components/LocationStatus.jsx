import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faLocationDot, faExclamationTriangle, faSpinner } from '@fortawesome/free-solid-svg-icons';

export default function LocationStatus({ userLocation, error }) {
  if (error) {
    return (
      <div className="bg-white rounded-xl shadow-logistics p-6">
        <div className="flex items-start space-x-3">
          <div className="bg-red-100 p-2 rounded-lg">
            <FontAwesomeIcon icon={faExclamationTriangle} className="text-red-600" />
          </div>
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-red-800">Location Error</h3>
            <p className="text-red-700 mt-1">{error}</p>
            <p className="text-sm text-red-600 mt-2">
              Please enable location services to track your position
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-xl shadow-logistics p-6">
      <div className="flex items-start space-x-3">
        <div className={`p-2 rounded-lg ${userLocation ? 'bg-green-100' : 'bg-blue-100'}`}>
          <FontAwesomeIcon
            icon={userLocation ? faLocationDot : faSpinner}
            className={`${userLocation ? 'text-green-600' : 'text-blue-600 animate-spin'}`}
          />
        </div>
        <div className="flex-1">
          <h3 className="text-lg font-semibold text-gray-900">
            {userLocation ? 'Current Location' : 'Detecting Location...'}
          </h3>
          {userLocation ? (
            <div className="mt-2 space-y-1">
              <p className="text-sm text-gray-600">
                <span className="font-medium">Latitude:</span> {userLocation.lat.toFixed(6)}
              </p>
              <p className="text-sm text-gray-600">
                <span className="font-medium">Longitude:</span> {userLocation.lng.toFixed(6)}
              </p>
              <div className="mt-3 flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span className="text-xs text-green-600 font-medium">Live Tracking Active</span>
              </div>
            </div>
          ) : (
            <p className="text-sm text-gray-600 mt-1">
              Waiting for GPS signal...
            </p>
          )}
        </div>
      </div>
    </div>
  );
}