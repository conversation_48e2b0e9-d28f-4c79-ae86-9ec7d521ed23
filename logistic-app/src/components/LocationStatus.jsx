export default function LocationStatus({ userLocation, error }) {
    if (error) {
      return (
        <div className="bg-red-100 text-red-700 p-3 rounded mb-4">
          <p>⚠️ {error}</p>
          <p className="text-sm mt-1">warehouse location</p>
        </div>
      );
    }
  
    return (
      <div className="bg-blue-50 p-3 rounded mb-4">
        {userLocation ? (
          <p>Current location: 
            {userLocation.lat.toFixed(4)}, 
            {userLocation.lng.toFixed(4)}
          </p>
        ) : (
          <p>Detecting your location...</p>
        )}
      </div>
    );
  }