import { useState, useEffect } from 'react';
import { GoogleMap, LoadScript, Marker, Polyline } from '@react-google-maps/api';
import Loader from './Loader';

const MAP_CONTAINER_STYLE = {
  width: '100%',
  height: '70vh',
  borderRadius: '8px',
  border: '1px solid #e2e8f0'
};

const DEFAULT_CENTER = { lat: 40.7128, lng: -74.0060 }; // NYC

export default function MapContainer({ setUserLocation, setError, userLocation }) {
  const [map, setMap] = useState(null);
  const [warehouse] = useState(() => {
    return userLocation 
      ? { lat: userLocation.lat + 0.02, lng: userLocation.lng + 0.01 } 
      : DEFAULT_CENTER;
  });

  // Get user location
  useEffect(() => {
    if (!navigator.geolocation) {
      setError('Geolocation not supported');
      return;
    }

    navigator.geolocation.getCurrentPosition(
      position => {
        const loc = {
          lat: position.coords.latitude,
          lng: position.coords.longitude
        };
        setUserLocation(loc);
      },
      err => {
        let message = 'Location detection failed';
        if (err.code === err.PERMISSION_DENIED) message = 'Location permission denied';
        if (err.code === err.TIMEOUT) message = 'Location request timed out';
        setError(message);
      },
      { timeout: 5000 }
    );
  }, []);

  return (
    <LoadScript
      googleMapsApiKey="YOUR_API_KEY"
      loadingElement={<Loader />}
    >
      <GoogleMap
        mapContainerStyle={MAP_CONTAINER_STYLE}
        center={userLocation || DEFAULT_CENTER}
        zoom={userLocation ? 14 : 10}
        onLoad={map => setMap(map)}
        options={{
          streetViewControl: false,
          mapTypeControl: false,
          fullscreenControl: false
        }}
      >
        {userLocation && (
          <>
            <Marker 
              position={userLocation} 
              label="📍" 
              title="Your Location" 
            />
            <Marker 
              position={warehouse} 
              label="🏭" 
              title="Warehouse" 
            />
            <Polyline
              path={[userLocation, warehouse]}
              options={{ strokeColor: "#FF0000", strokeOpacity: 0.8, strokeWeight: 4 }}
            />
          </>
        )}
      </GoogleMap>
    </LoadScript>
  );
}