import { useState, useEffect } from 'react';
import { GoogleMap, LoadScript, Marker, Polyline } from '@react-google-maps/api';
import Loader from './Loader';

const MAP_CONTAINER_STYLE = {
  width: '100%',
  height: '600px',
};

const DEFAULT_CENTER = { lat: 40.7128, lng: -74.0060 }; // NYC

export default function MapContainer({ setUserLocation, setError, userLocation }) {
  const [map, setMap] = useState(null);
  const [isTracking, setIsTracking] = useState(false);
  const [watchId, setWatchId] = useState(null);
  const [locationHistory, setLocationHistory] = useState([]);

  const [warehouse] = useState(() => {
    return userLocation
      ? { lat: userLocation.lat + 0.02, lng: userLocation.lng + 0.01 }
      : { lat: DEFAULT_CENTER.lat + 0.02, lng: DEFAULT_CENTER.lng + 0.01 };
  });

  // Sample delivery points for demonstration
  const [deliveryPoints] = useState([
    { id: 1, lat: userLocation?.lat + 0.01 || DEFAULT_CENTER.lat + 0.01, lng: userLocation?.lng + 0.005 || DEFAULT_CENTER.lng + 0.005, status: 'pending', address: '123 Main St' },
    { id: 2, lat: userLocation?.lat - 0.01 || DEFAULT_CENTER.lat - 0.01, lng: userLocation?.lng + 0.01 || DEFAULT_CENTER.lng + 0.01, status: 'completed', address: '456 Oak Ave' },
    { id: 3, lat: userLocation?.lat + 0.005 || DEFAULT_CENTER.lat + 0.005, lng: userLocation?.lng - 0.01 || DEFAULT_CENTER.lng - 0.01, status: 'in-progress', address: '789 Pine Rd' },
  ]);

  // Start real-time location tracking
  const startTracking = () => {
    if (!navigator.geolocation) {
      setError('Geolocation not supported');
      return;
    }

    setIsTracking(true);
    const id = navigator.geolocation.watchPosition(
      position => {
        const loc = {
          lat: position.coords.latitude,
          lng: position.coords.longitude,
          timestamp: Date.now(),
          accuracy: position.coords.accuracy
        };
        setUserLocation(loc);
        setLocationHistory(prev => [...prev.slice(-50), loc]); // Keep last 50 positions
        setError(null);
      },
      err => {
        let message = 'Location tracking failed';
        if (err.code === err.PERMISSION_DENIED) message = 'Location permission denied';
        if (err.code === err.TIMEOUT) message = 'Location request timed out';
        setError(message);
        setIsTracking(false);
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 30000
      }
    );
    setWatchId(id);
  };

  // Stop real-time location tracking
  const stopTracking = () => {
    if (watchId) {
      navigator.geolocation.clearWatch(watchId);
      setWatchId(null);
    }
    setIsTracking(false);
  };

  // Get initial location and start tracking
  useEffect(() => {
    if (!navigator.geolocation) {
      setError('Geolocation not supported');
      return;
    }

    // Get initial position
    navigator.geolocation.getCurrentPosition(
      position => {
        const loc = {
          lat: position.coords.latitude,
          lng: position.coords.longitude,
          timestamp: Date.now(),
          accuracy: position.coords.accuracy
        };
        setUserLocation(loc);
        setLocationHistory([loc]);
        // Start continuous tracking after getting initial position
        startTracking();
      },
      err => {
        let message = 'Location detection failed';
        if (err.code === err.PERMISSION_DENIED) message = 'Location permission denied';
        if (err.code === err.TIMEOUT) message = 'Location request timed out';
        setError(message);
      },
      { timeout: 10000, enableHighAccuracy: true }
    );

    // Cleanup on unmount
    return () => {
      stopTracking();
    };
  }, []);

  return (
    <LoadScript
      googleMapsApiKey={import.meta.env.VITE_GOOGLE_MAPS_API_KEY}
      loadingElement={<Loader />}
    >
      <GoogleMap
        mapContainerStyle={MAP_CONTAINER_STYLE}
        center={userLocation || DEFAULT_CENTER}
        zoom={userLocation ? 14 : 10}
        onLoad={map => setMap(map)}
        options={{
          streetViewControl: false,
          mapTypeControl: true,
          fullscreenControl: true,
          zoomControl: true,
          styles: [
            {
              featureType: "poi",
              elementType: "labels",
              stylers: [{ visibility: "off" }]
            }
          ]
        }}
      >
        {userLocation && (
          <>
            {/* Current Location Marker */}
            <Marker
              position={userLocation}
              icon={{
                url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                  <svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="16" cy="16" r="12" fill="#0ea5e9" stroke="#ffffff" stroke-width="3"/>
                    <circle cx="16" cy="16" r="6" fill="#ffffff"/>
                  </svg>
                `),
                scaledSize: new window.google.maps.Size(32, 32),
                anchor: new window.google.maps.Point(16, 16)
              }}
              title="Your Current Location"
            />

            {/* Warehouse Marker */}
            <Marker
              position={warehouse}
              icon={{
                url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                  <svg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg">
                    <rect x="5" y="15" width="30" height="20" fill="#059669" stroke="#ffffff" stroke-width="2" rx="2"/>
                    <polygon points="20,5 10,15 30,15" fill="#059669" stroke="#ffffff" stroke-width="2"/>
                    <rect x="15" y="20" width="4" height="8" fill="#ffffff"/>
                    <rect x="21" y="20" width="4" height="8" fill="#ffffff"/>
                  </svg>
                `),
                scaledSize: new window.google.maps.Size(40, 40),
                anchor: new window.google.maps.Point(20, 35)
              }}
              title="Distribution Center"
            />

            {/* Delivery Points */}
            {deliveryPoints.map((point) => (
              <Marker
                key={point.id}
                position={{ lat: point.lat, lng: point.lng }}
                icon={{
                  url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                    <svg width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <circle cx="12" cy="12" r="10" fill="${
                        point.status === 'completed' ? '#10b981' :
                        point.status === 'in-progress' ? '#f59e0b' : '#ef4444'
                      }" stroke="#ffffff" stroke-width="2"/>
                      <text x="12" y="16" text-anchor="middle" fill="white" font-size="12" font-weight="bold">
                        ${point.id}
                      </text>
                    </svg>
                  `),
                  scaledSize: new window.google.maps.Size(24, 24),
                  anchor: new window.google.maps.Point(12, 12)
                }}
                title={`Delivery ${point.id}: ${point.address} (${point.status})`}
              />
            ))}

            {/* Location History Trail */}
            {locationHistory.length > 1 && (
              <Polyline
                path={locationHistory.map(loc => ({ lat: loc.lat, lng: loc.lng }))}
                options={{
                  strokeColor: "#10b981",
                  strokeOpacity: 0.6,
                  strokeWeight: 3,
                  geodesic: true
                }}
              />
            )}

            {/* Route from warehouse to current location */}
            <Polyline
              path={[warehouse, userLocation]}
              options={{
                strokeColor: "#0ea5e9",
                strokeOpacity: 0.8,
                strokeWeight: 4,
                icons: [{
                  icon: {
                    path: window.google.maps.SymbolPath.FORWARD_CLOSED_ARROW,
                    scale: 3,
                    fillColor: "#0ea5e9",
                    fillOpacity: 1,
                    strokeWeight: 1,
                    strokeColor: "#ffffff"
                  },
                  offset: '100%'
                }]
              }}
            />

            {/* Tracking Status Indicator */}
            <div className="absolute top-4 right-4 bg-white rounded-lg shadow-lg p-3 z-10">
              <div className="flex items-center space-x-2">
                <div className={`w-3 h-3 rounded-full ${isTracking ? 'bg-green-500 animate-pulse' : 'bg-gray-400'}`}></div>
                <span className="text-sm font-medium text-gray-700">
                  {isTracking ? 'Live Tracking' : 'Tracking Stopped'}
                </span>
              </div>
              {userLocation?.accuracy && (
                <p className="text-xs text-gray-500 mt-1">
                  Accuracy: ±{Math.round(userLocation.accuracy)}m
                </p>
              )}
            </div>
          </>
        )}
      </GoogleMap>
    </LoadScript>
  );
}