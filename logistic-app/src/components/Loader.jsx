import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faMapMarkedAlt } from '@fortawesome/free-solid-svg-icons';

export default function Loader() {
  return (
    <div className="flex flex-col items-center justify-center h-96 bg-gradient-to-br from-logistics-50 to-logistics-100">
      <div className="relative">
        <div className="animate-spin rounded-full h-16 w-16 border-4 border-logistics-200 border-t-logistics-500"></div>
        <div className="absolute inset-0 flex items-center justify-center">
          <FontAwesomeIcon icon={faMapMarkedAlt} className="text-logistics-500 text-lg" />
        </div>
      </div>
      <div className="mt-4 text-center">
        <p className="text-logistics-700 font-medium">Loading Map...</p>
        <p className="text-logistics-600 text-sm mt-1">Initializing GPS tracking</p>
      </div>
    </div>
  );
}